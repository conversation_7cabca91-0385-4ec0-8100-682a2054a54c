import { createClient } from '@/lib/supabase/server'
import { NoteAttachment } from '@/types/notes'
import { DatabaseNoteAttachment, DatabaseError } from './types'

/**
 * Get all attachments for a specific note
 */
export async function getAttachmentsByNoteId(noteId: string): Promise<NoteAttachment[]> {
  try {
    const supabase = await createClient()

    const { data: attachments, error } = await supabase
      .from('note_attachments')
      .select('*')
      .eq('note_id', noteId)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Database error fetching attachments:', error)
      throw new DatabaseError('Could not fetch attachments from the database', error)
    }

    return formatAttachmentsFromDatabase(attachments || [])
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error fetching attachments:', error)
    throw new DatabaseError('Unexpected error occurred while fetching attachments')
  }
}

/**
 * Create multiple attachments for a note
 */
export async function createAttachments(
  noteId: string,
  attachmentUrls: string[]
): Promise<NoteAttachment[]> {
  try {
    if (!attachmentUrls.length) {
      return []
    }

    // Limit to maximum 3 attachments
    if (attachmentUrls.length > 3) {
      throw new DatabaseError('Maximum 3 attachments allowed per note')
    }

    const supabase = await createClient()

    // Check current attachment count
    const { count: currentCount, error: countError } = await supabase
      .from('note_attachments')
      .select('*', { count: 'exact', head: true })
      .eq('note_id', noteId)

    if (countError) {
      console.error('Database error checking attachment count:', countError)
      throw new DatabaseError('Could not check current attachment count', countError)
    }

    const totalAfterInsert = (currentCount || 0) + attachmentUrls.length
    if (totalAfterInsert > 3) {
      throw new DatabaseError(`Cannot add ${attachmentUrls.length} attachments. Note already has ${currentCount} attachments. Maximum 3 allowed.`)
    }

    // Prepare attachment records
    const attachmentRecords = attachmentUrls.map(url => ({
      note_id: noteId,
      url: url
    }))

    const { data: attachments, error } = await supabase
      .from('note_attachments')
      .insert(attachmentRecords)
      .select()

    if (error) {
      console.error('Database error creating attachments:', error)
      throw new DatabaseError('Could not create attachments in the database', error)
    }

    return formatAttachmentsFromDatabase(attachments || [])
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error creating attachments:', error)
    throw new DatabaseError('Unexpected error occurred while creating attachments')
  }
}

/**
 * Delete a specific attachment
 */
export async function deleteAttachment(attachmentId: string, userId: string): Promise<void> {
  try {
    const supabase = await createClient()

    // Verify the attachment belongs to a note owned by the user
    const { data: attachment, error: checkError } = await supabase
      .from('note_attachments')
      .select(`
        id,
        notes!inner (
          user_id
        )
      `)
      .eq('id', attachmentId)
      .single()

    if (checkError || !attachment) {
      throw new DatabaseError('Attachment not found or access denied')
    }

    // Type assertion for the nested structure
    const attachmentWithNote = attachment as any
    if (attachmentWithNote.notes.user_id !== userId) {
      throw new DatabaseError('Access denied: attachment belongs to another user')
    }

    const { error: deleteError } = await supabase
      .from('note_attachments')
      .delete()
      .eq('id', attachmentId)

    if (deleteError) {
      console.error('Database error deleting attachment:', deleteError)
      throw new DatabaseError('Could not delete attachment from the database', deleteError)
    }
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error deleting attachment:', error)
    throw new DatabaseError('Unexpected error occurred while deleting attachment')
  }
}

/**
 * Delete all attachments for a note (used when deleting a note)
 */
export async function deleteAttachmentsByNoteId(noteId: string): Promise<void> {
  try {
    const supabase = await createClient()

    const { error } = await supabase
      .from('note_attachments')
      .delete()
      .eq('note_id', noteId)

    if (error) {
      console.error('Database error deleting attachments by note ID:', error)
      throw new DatabaseError('Could not delete attachments from the database', error)
    }
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error deleting attachments by note ID:', error)
    throw new DatabaseError('Unexpected error occurred while deleting attachments')
  }
}

/**
 * Get attachment count for a note
 */
export async function getAttachmentCount(noteId: string): Promise<number> {
  try {
    const supabase = await createClient()

    const { count, error } = await supabase
      .from('note_attachments')
      .select('*', { count: 'exact', head: true })
      .eq('note_id', noteId)

    if (error) {
      console.error('Database error counting attachments:', error)
      throw new DatabaseError('Could not count attachments', error)
    }

    return count || 0
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error counting attachments:', error)
    throw new DatabaseError('Unexpected error occurred while counting attachments')
  }
}

/**
 * Format database attachments to client format
 */
function formatAttachmentsFromDatabase(attachments: DatabaseNoteAttachment[]): NoteAttachment[] {
  return attachments.map(attachment => ({
    id: attachment.id,
    note_id: attachment.note_id,
    url: attachment.url,
    created_at: attachment.created_at
  }))
}
