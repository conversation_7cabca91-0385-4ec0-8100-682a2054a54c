import { useState, useRef } from "react"
import { Send, Edit3, <PERSON>c<PERSON>, ImageIcon, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { InlineError } from "@/components/ui/inline-error"
import { useMultipleFileUpload } from "@/hooks/useFileUpload"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"

interface NoteComposerProps {
  onSubmit: (content: string, attachmentUrls?: string[]) => Promise<void>
  userEmail?: string
  isSubmitting?: boolean
}

export function NoteComposer({ onSubmit, userEmail, isSubmitting = false }: NoteComposerProps) {
  const [content, setContent] = useState("")
  const [isMarkdownMode, setIsMarkdownMode] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [fileError, setFileError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const {
    selectedFiles,
    uploadingFiles,
    addFile,
    removeFile,
    uploadAll,
    canAddMore
  } = useMultipleFileUpload(3)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setFileError(null)

    // Add each selected file
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const result = addFile(file)

      if (!result.success) {
        setFileError(result.error || 'Ошибка при выборе файла')
        break
      }
    }
  }

  const handleRemoveFile = (fileId: string) => {
    removeFile(fileId)
    setFileError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }



  const handleSubmit = async () => {
    if ((!content.trim() && selectedFiles.length === 0) || submitting || isSubmitting || uploadingFiles) return

    setSubmitting(true)
    setFileError(null)

    try {
      let attachmentUrls: string[] = []

      // Upload files if selected
      if (selectedFiles.length > 0) {
        try {
          attachmentUrls = await uploadAll()
          if (selectedFiles.length > 0 && attachmentUrls.length === 0) {
            // File upload failed, don't proceed
            return
          }
        } catch (error) {
          setFileError(error instanceof Error ? error.message : 'Ошибка загрузки файлов')
          return
        }
      }

      await onSubmit(content.trim(), attachmentUrls.length > 0 ? attachmentUrls : undefined)
      setContent("")
      setIsMarkdownMode(false)
      // Clear all files
      selectedFiles.forEach(file => handleRemoveFile(file.id))
    } finally {
      setSubmitting(false)
    }
  }

  const isDisabled = (!content.trim() && selectedFiles.length === 0) || submitting || isSubmitting || uploadingFiles

  return (
    <Card className="py-1">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback>{userEmail?.[0]?.toUpperCase() || 'U'}</AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium">Новая заметка</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMarkdownMode(!isMarkdownMode)}
              className={isMarkdownMode ? "bg-muted" : ""}
            >
              <Edit3 className="h-4 w-4 mr-1" />
              Markdown
            </Button>
          </div>

          <Textarea
            placeholder={isMarkdownMode ? "Введите текст с поддержкой Markdown..." : "Введите вашу заметку..."}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="min-h-[100px] resize-none"
          />

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileSelect}
            className="hidden"
          />

          {/* Files preview */}
          {selectedFiles.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground">
                Прикрепленные изображения ({selectedFiles.length}/3):
              </div>
              {selectedFiles.map((fileWithPreview) => (
                <div key={fileWithPreview.id} className="border rounded-md p-3 bg-muted/50">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-xs text-muted-foreground">
                      {fileWithPreview.file.name} ({Math.round(fileWithPreview.file.size / 1024)} KB)
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveFile(fileWithPreview.id)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="relative">
                    <img
                      src={fileWithPreview.previewUrl}
                      alt="Preview"
                      className="max-w-full max-h-48 rounded object-contain"
                    />
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* File error display */}
          {fileError && (
            <InlineError message={fileError} />
          )}

          {isMarkdownMode && content && (
            <div className="border rounded-md p-3 bg-muted/50">
              <div className="text-xs text-muted-foreground mb-2">Предварительный просмотр:</div>
              <div className="prose max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" disabled>
                <Paperclip className="h-4 w-4 mr-1" />
                Файл
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploadingFiles || !canAddMore}
              >
                <ImageIcon className="h-4 w-4 mr-1" />
                Изображение {!canAddMore && '(макс. 3)'}
              </Button>
            </div>
            <Button onClick={handleSubmit} disabled={isDisabled}>
              <Send className="h-4 w-4 mr-1" />
              {uploadingFiles ? 'Загрузка...' : (submitting || isSubmitting) ? 'Сохранение...' : 'Отправить'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
