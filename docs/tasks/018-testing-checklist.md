# Чеклист тестирования: Множественные изображения

## Тестирование базы данных

### ✅ Миграция данных
- [x] Создана таблица `note_attachments`
- [x] Настроены RLS политики
- [x] Мигрированы существующие данные из `attachment_url`
- [x] Удалено поле `attachment_url` из таблицы `notes`
- [x] Проверена целостность данных

### Тестирование ограничений
- [ ] Проверить ограничение на максимум 3 вложения на заметку
- [ ] Проверить каскадное удаление вложений при удалении заметки
- [ ] Проверить RLS политики (пользователи видят только свои вложения)

## Тестирование API

### Генерация signed URLs
- [ ] `POST /api/notes/upload-url` с 1 файлом
- [ ] `POST /api/notes/upload-url` с 3 файлами
- [ ] `POST /api/notes/upload-url` с более чем 3 файлами (должна быть ошибка)
- [ ] Валидация типов файлов
- [ ] Валидация размеров файлов

### Создание заметок
- [ ] Создание заметки только с текстом
- [ ] Создание заметки с 1 изображением
- [ ] Создание заметки с 3 изображениями
- [ ] Создание заметки только с изображениями (без текста)
- [ ] Проверка AI анализа с изображениями

### Получение заметок
- [ ] Проверить, что заметки возвращаются с массивом `attachments`
- [ ] Проверить JOIN запрос для получения вложений
- [ ] Проверить производительность запросов

## Тестирование Frontend

### Компонент NoteComposer
- [ ] Выбор 1 изображения
- [ ] Выбор множественных изображений (до 3)
- [ ] Попытка выбрать более 3 изображений
- [ ] Удаление отдельных изображений
- [ ] Предпросмотр всех выбранных изображений
- [ ] Отображение лимитов ("макс. 3")
- [ ] Загрузка файлов и создание заметки
- [ ] Обработка ошибок загрузки

### Компонент NotesList
- [ ] Отображение заметки с 1 изображением
- [ ] Отображение заметки с множественными изображениями в сетке
- [ ] Lazy loading изображений
- [ ] Обработка ошибок загрузки изображений

### Хуки
- [ ] `useMultipleFileUpload` - добавление файлов
- [ ] `useMultipleFileUpload` - удаление файлов
- [ ] `useMultipleFileUpload` - загрузка всех файлов
- [ ] `useFileUpload` (обратная совместимость)

## Тестирование AI анализа

### Анализ с изображениями
- [ ] Анализ заметки с 1 изображением
- [ ] Анализ заметки с множественными изображениями (использует первое)
- [ ] Анализ заметки только с изображениями
- [ ] Повторный анализ заметок с изображениями

### Генерация тегов и описаний
- [ ] Проверить, что теги учитывают содержимое изображений
- [ ] Проверить, что описания включают информацию об изображениях
- [ ] Проверить обработку ошибок AI анализа

## Тестирование производительности

### Загрузка файлов
- [ ] Параллельная загрузка множественных файлов
- [ ] Время загрузки больших файлов (до 10MB)
- [ ] Обработка сетевых ошибок

### Отображение
- [ ] Время загрузки страницы с множественными изображениями
- [ ] Производительность скролла в ленте заметок
- [ ] Использование памяти при предпросмотре файлов

## Тестирование безопасности

### RLS политики
- [ ] Пользователь A не может видеть вложения пользователя B
- [ ] Пользователь A не может создавать вложения для заметок пользователя B
- [ ] Пользователь A не может удалять вложения пользователя B

### Валидация
- [ ] Проверка типов файлов на сервере
- [ ] Проверка размеров файлов на сервере
- [ ] Проверка лимитов количества файлов

## Тестирование граничных случаев

### Ошибки сети
- [ ] Потеря соединения во время загрузки
- [ ] Таймауты при загрузке больших файлов
- [ ] Ошибки Supabase Storage

### Некорректные данные
- [ ] Поврежденные файлы изображений
- [ ] Файлы с некорректными расширениями
- [ ] Очень большие файлы (>10MB)

### Состояния UI
- [ ] Загрузка множественных файлов одновременно
- [ ] Отмена загрузки
- [ ] Повторная попытка после ошибки

## Результаты тестирования

### ✅ Пройденные тесты
- Миграция базы данных
- Создание таблицы note_attachments
- Обновление типов данных
- Компиляция без ошибок

### ⏳ Тесты в процессе
- Функциональное тестирование UI
- Тестирование API endpoints
- Проверка производительности

### ❌ Не пройденные тесты
- (пока нет)

## Следующие шаги
1. Запустить приложение и протестировать UI
2. Проверить создание заметок с множественными изображениями
3. Протестировать AI анализ
4. Оптимизировать производительность при необходимости
