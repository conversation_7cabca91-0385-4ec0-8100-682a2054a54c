# Задача 018: Поддержка множественных изображений (до 3-х) с отдельной таблицей note_attachments

## Описание задачи
Реализовать возможность добавления нескольких изображений (до 3-х) к одной заметке, используя классический реляционный подход с отдельной таблицей `note_attachments`. Это заменит текущее решение с одним полем `attachment_url` в таблице `notes`.

## Анализ текущего состояния

### Существующая архитектура:
- **База данных**: Таблица `notes` с полем `attachment_url TEXT NULL` для одного изображения
- **Storage**: Supabase Storage bucket `note_attachments` с RLS политиками
- **API**: 
  - `POST /api/notes/upload-url` - генерация signed URL для одного файла
  - `POST /api/notes` - создание заметки с одним `attachment_url`
- **Frontend**: 
  - `NoteComposer` - загрузка одного изображения
  - `NotesList` - отображение одного изображения
- **AI**: Мультимодальный анализ с поддержкой одного изображения

### Проблемы текущего подхода:
1. Ограничение одним изображением на заметку
2. Невозможность добавления метаданных к изображениям
3. Сложность расширения функциональности в будущем

## Цели
- [ ] Создать новую таблицу `note_attachments` для хранения множественных вложений
- [ ] Удалить поле `attachment_url` из таблицы `notes`
- [ ] Обновить API для работы с массивом изображений
- [ ] Модифицировать UI для выбора и отображения до 3-х изображений
- [ ] Расширить AI анализ для обработки множественных изображений
- [ ] Обеспечить миграцию существующих данных

## Детальный план реализации

### Этап 1: Создание новой схемы БД
- [ ] Создать таблицу `note_attachments` с полями:
  - `id UUID PRIMARY KEY`
  - `note_id UUID REFERENCES notes(id) ON DELETE CASCADE`
  - `url TEXT NOT NULL`
  - `created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()`
- [ ] Настроить RLS политики для `note_attachments`
- [ ] Создать индексы для производительности
- [ ] Добавить ограничение на максимум 3 вложения на заметку

### Этап 2: Миграция существующих данных
- [ ] Создать миграцию для переноса данных из `notes.attachment_url` в `note_attachments`
- [ ] Удалить поле `attachment_url` из таблицы `notes`
- [ ] Обновить индексы и ограничения

### Этап 3: Обновление Data Access Layer
- [ ] Создать новый файл `lib/data/attachments.ts` для работы с вложениями
- [ ] Обновить `lib/data/notes.ts` для работы с новой схемой
- [ ] Обновить типы в `lib/data/types.ts` и `types/notes.ts`

### Этап 4: Обновление API
- [ ] Модифицировать `POST /api/notes/upload-url` для генерации массива signed URLs
- [ ] Обновить `POST /api/notes` для создания заметки с массивом вложений
- [ ] Добавить API для управления вложениями (добавление/удаление)

### Этап 5: Обновление Frontend
- [ ] Модифицировать `NoteComposer` для выбора до 3-х изображений
- [ ] Обновить `NotesList` для отображения галереи изображений
- [ ] Создать компонент `ImageGallery` для отображения множественных изображений
- [ ] Обновить хук `useFileUpload` для работы с массивом файлов

### Этап 6: Расширение AI анализа
- [ ] Обновить `lib/ai/analyze.ts` для обработки массива изображений
- [ ] Модифицировать промпты для анализа множественных изображений
- [ ] Обеспечить корректную обработку ошибок при анализе

### Этап 7: Тестирование и оптимизация
- [ ] Протестировать создание заметок с множественными изображениями
- [ ] Проверить корректность миграции данных
- [ ] Оптимизировать производительность загрузки
- [ ] Протестировать AI анализ с несколькими изображениями

## Технические детали

### Новая схема БД:
```sql
-- Таблица для хранения вложений
CREATE TABLE note_attachments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    note_id UUID REFERENCES notes(id) ON DELETE CASCADE NOT NULL,
    url TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- Индексы для производительности
CREATE INDEX idx_note_attachments_note_id ON note_attachments(note_id);
CREATE INDEX idx_note_attachments_created_at ON note_attachments(created_at);

-- Ограничение на максимум 3 вложения на заметку
ALTER TABLE note_attachments ADD CONSTRAINT max_attachments_per_note 
CHECK ((SELECT COUNT(*) FROM note_attachments WHERE note_id = note_attachments.note_id) <= 3);

-- RLS политики
ALTER TABLE note_attachments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view attachments of their notes" ON note_attachments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM notes 
            WHERE notes.id = note_attachments.note_id 
            AND notes.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create attachments for their notes" ON note_attachments
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM notes 
            WHERE notes.id = note_attachments.note_id 
            AND notes.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete attachments of their notes" ON note_attachments
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM notes 
            WHERE notes.id = note_attachments.note_id 
            AND notes.user_id = auth.uid()
        )
    );
```

### Обновленные типы:
```typescript
// types/notes.ts
export interface Note {
  id: string
  content: string
  content_type: 'text' | 'file' | 'link'
  tags: string[]
  created_at: string
  updated_at: string
  summary_ai: string | null
  attachments: NoteAttachment[]  // Заменяет attachment_url
  isAnalyzing?: boolean
  aiAnalysisFailed?: boolean
}

export interface NoteAttachment {
  id: string
  note_id: string
  url: string
  created_at: string
}

export interface CreateNoteRequest {
  content: string
  content_type?: 'text' | 'file' | 'link'
  attachment_urls?: string[]  // Заменяет attachment_url
}
```

### Новые API endpoints:
- `POST /api/notes/upload-urls` - генерация массива signed URLs (до 3-х)
- `POST /api/notes/{id}/attachments` - добавление вложения к существующей заметке
- `DELETE /api/notes/{id}/attachments/{attachmentId}` - удаление конкретного вложения

## Преимущества нового подхода

### Гибкость и расширяемость:
- Легко добавить метаданные к каждому изображению (alt-текст, размер, тип)
- Возможность изменения лимита вложений без изменения схемы
- Простое добавление новых типов вложений в будущем

### Производительность:
- Эффективные запросы с JOIN для получения заметок с вложениями
- Возможность индексирования по URL вложений
- Атомарные операции для управления вложениями

### Целостность данных:
- Каскадное удаление вложений при удалении заметки
- Возможность добавления UNIQUE constraints
- Лучший контроль над ограничениями

## Риски и ограничения

### Технические риски:
- Увеличение сложности запросов (требуются JOIN)
- Больше кода для управления вложениями
- Необходимость миграции существующих данных

### Минимизация рисков:
- Использование Supabase автоматических JOIN через `select('*, note_attachments(*)')`
- Создание вспомогательных функций в DAL для упрощения работы
- Тщательное тестирование миграции на копии данных

## Переменные окружения
Используются существующие:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
OPENROUTER_API_KEY=your_openrouter_api_key
```

## Статус: Планирование
Задача находится в стадии планирования. Готов к началу реализации.
