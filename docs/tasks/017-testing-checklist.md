# Чеклист тестирования загрузки изображений

## Функциональное тестирование

### 1. Загрузка изображений
- [x] Кнопка "Изображение" активна в NoteComposer
- [x] Клик по кнопке открывает диалог выбора файла
- [x] Можно выбрать изображение (JPEG, PNG, WebP, GIF)
- [x] Отклоняются неподдерживаемые форматы
- [x] Отклоняются файлы больше 10MB
- [x] Показывается предпросмотр выбранного изображения
- [x] Можно удалить выбранное изображение (кнопка X)

### 2. Создание заметки с изображением
- [x] Можно создать заметку только с изображением (без текста)
- [x] Можно создать заметку с текстом и изображением
- [x] Показывается индикатор "Загрузка..." во время upload
- [x] Показывается индикатор "Сохранение..." во время создания заметки
- [x] После успешного создания форма очищается

### 3. Отображение заметок с изображениями
- [x] Изображения корректно отображаются в NotesList
- [x] Изображения имеют разумные размеры (max-h-96)
- [x] Изображения загружаются с lazy loading
- [x] Текст заметки отображается под изображением (если есть)

### 4. AI анализ мультимодального контента
- [x] AI анализирует заметки с изображениями
- [x] Генерируются релевантные теги на основе изображения
- [x] Создается саммари, описывающее изображение
- [x] Анализ работает для заметок с текстом + изображением
- [x] Анализ работает для заметок только с изображением

### 5. Безопасность и производительность
- [x] Файлы загружаются в правильный bucket (note_attachments)
- [x] Пользователи видят только свои изображения
- [x] Signed URLs работают корректно
- [x] Нет утечек памяти при предпросмотре изображений
- [x] Обработка ошибок при неудачной загрузке

## Тестовые сценарии

### Сценарий 1: Создание заметки с изображением
1. Открыть приложение
2. Кликнуть "Изображение" в NoteComposer
3. Выбрать изображение (например, скриншот)
4. Добавить текст: "Тестовая заметка с изображением"
5. Нажать "Отправить"
6. Проверить, что заметка создалась с изображением
7. Дождаться AI анализа и проверить теги

### Сценарий 2: Только изображение без текста
1. Кликнуть "Изображение"
2. Выбрать изображение
3. НЕ добавлять текст
4. Нажать "Отправить"
5. Проверить создание заметки
6. Проверить AI анализ изображения

### Сценарий 3: Обработка ошибок
1. Попробовать загрузить файл неподдерживаемого формата
2. Попробовать загрузить файл больше 10MB
3. Проверить корректные сообщения об ошибках

## Известные ограничения
- Максимальный размер файла: 10MB
- Поддерживаемые форматы: JPEG, PNG, WebP, GIF
- AI анализ зависит от доступности OpenRouter API
- Изображения хранятся в Supabase Storage (влияет на квоты)
