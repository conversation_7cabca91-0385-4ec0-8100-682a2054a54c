import { useState } from 'react'
import { validateFile } from '@/lib/storage/validation'
import { STORAGE_ERRORS } from '@/lib/storage/config'

export interface FileWithPreview {
  file: File
  previewUrl: string
  id: string
}

export interface UseMultipleFileUploadResult {
  selectedFiles: FileWithPreview[]
  uploadingFiles: boolean
  addFile: (file: File) => { success: boolean; error?: string }
  removeFile: (id: string) => void
  uploadAll: () => Promise<string[]>
  canAddMore: boolean
}

export function useMultipleFileUpload(maxFiles: number = 3): UseMultipleFileUploadResult {
  const [selectedFiles, setSelectedFiles] = useState<FileWithPreview[]>([])
  const [uploadingFiles, setUploadingFiles] = useState(false)

  const addFile = (file: File): { success: boolean; error?: string } => {
    // Check if we can add more files
    if (selectedFiles.length >= maxFiles) {
      return { success: false, error: `Максимум ${maxFiles} файлов разрешено` }
    }

    // Validate file using centralized validation
    const validation = validateFile({
      name: file.name,
      size: file.size,
      type: file.type
    })

    if (!validation.isValid) {
      return { success: false, error: validation.error }
    }

    // Create preview URL and unique ID
    const previewUrl = URL.createObjectURL(file)
    const id = `${Date.now()}-${Math.random().toString(36).substring(2)}`

    const fileWithPreview: FileWithPreview = {
      file,
      previewUrl,
      id
    }

    setSelectedFiles(prev => [...prev, fileWithPreview])
    return { success: true }
  }

  const removeFile = (id: string) => {
    setSelectedFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id)
      if (fileToRemove) {
        URL.revokeObjectURL(fileToRemove.previewUrl)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  const uploadAll = async (): Promise<string[]> => {
    if (selectedFiles.length === 0) {
      return []
    }

    try {
      setUploadingFiles(true)

      // Prepare file info for API
      const filesInfo = selectedFiles.map(({ file }) => ({
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      }))

      // Get upload URLs for all files
      const uploadResponse = await fetch('/api/notes/upload-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ files: filesInfo })
      })

      if (!uploadResponse.ok) {
        const error = await uploadResponse.json()
        throw new Error(error.error || STORAGE_ERRORS.UPLOAD_URL_FAILED)
      }

      const { uploads } = await uploadResponse.json()

      // Upload all files in parallel
      const uploadPromises = selectedFiles.map(async ({ file }, index) => {
        const { uploadUrl, publicUrl } = uploads[index]

        const uploadFileResponse = await fetch(uploadUrl, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': file.type
          }
        })

        if (!uploadFileResponse.ok) {
          throw new Error(`${STORAGE_ERRORS.UPLOAD_FAILED}: ${file.name}`)
        }

        return publicUrl
      })

      const publicUrls = await Promise.all(uploadPromises)
      return publicUrls
    } catch (error) {
      console.error('Files upload error:', error)
      throw error
    } finally {
      setUploadingFiles(false)
    }
  }

  const canAddMore = selectedFiles.length < maxFiles

  return {
    selectedFiles,
    uploadingFiles,
    addFile,
    removeFile,
    uploadAll,
    canAddMore
  }
}

// Legacy single file upload hook for backward compatibility
export interface UseFileUploadResult {
  selectedFile: File | null
  filePreviewUrl: string | null
  uploadingFile: boolean
  selectFile: (file: File) => { success: boolean; error?: string }
  removeFile: () => void
  upload: (file: File) => Promise<string | null>
}

export function useFileUpload(): UseFileUploadResult {
  const multipleUpload = useMultipleFileUpload(1)

  const selectFile = (file: File): { success: boolean; error?: string } => {
    // Remove existing file if any
    if (multipleUpload.selectedFiles.length > 0) {
      multipleUpload.removeFile(multipleUpload.selectedFiles[0].id)
    }
    return multipleUpload.addFile(file)
  }

  const removeFile = () => {
    if (multipleUpload.selectedFiles.length > 0) {
      multipleUpload.removeFile(multipleUpload.selectedFiles[0].id)
    }
  }

  const upload = async (_file: File): Promise<string | null> => {
    const urls = await multipleUpload.uploadAll()
    return urls.length > 0 ? urls[0] : null
  }

  const selectedFile = multipleUpload.selectedFiles.length > 0 ? multipleUpload.selectedFiles[0].file : null
  const filePreviewUrl = multipleUpload.selectedFiles.length > 0 ? multipleUpload.selectedFiles[0].previewUrl : null

  return {
    selectedFile,
    filePreviewUrl,
    uploadingFile: multipleUpload.uploadingFiles,
    selectFile,
    removeFile,
    upload
  }
}
