import { NextRequest } from 'next/server'
import { generateUploadUrl } from '@/lib/storage/upload'
import { requireAuth } from '@/lib/api/auth-middleware'
import { ApiResponses, handleApiError } from '@/lib/api/responses'
import { z } from 'zod'

// File info schema for individual files
const fileInfoSchema = z.object({
  fileName: z.string().min(1, 'Имя файла обязательно'),
  fileSize: z.number().positive('Размер файла должен быть положительным'),
  fileType: z.string().min(1, 'Тип файла обязателен')
})

// Request validation schema for multiple files
const uploadUrlRequestSchema = z.object({
  files: z.array(fileInfoSchema).min(1, 'Минимум один файл обязателен').max(3, 'Максимум 3 файла разрешено')
})

export type UploadUrlRequest = z.infer<typeof uploadUrlRequestSchema>

export interface UploadUrlResponse {
  uploadUrl: string
  filePath: string
  publicUrl: string
}

export interface MultipleUploadUrlResponse {
  uploads: UploadUrlResponse[]
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth()
    if (!authResult.success) {
      return authResult.response
    }

    const user = authResult.user

    // Parse and validate request body
    const body = await request.json()
    const validation = uploadUrlRequestSchema.safeParse(body)

    if (!validation.success) {
      console.error('Upload URL validation error:', validation.error)
      const errorMessage = validation.error.errors.map(e => e.message).join(', ')
      return ApiResponses.badRequest(`Ошибка валидации: ${errorMessage}`)
    }

    const { files } = validation.data

    // Generate upload URLs for all files
    const uploads: UploadUrlResponse[] = []

    for (const file of files) {
      const result = await generateUploadUrl(user.id, {
        name: file.fileName,
        size: file.fileSize,
        type: file.fileType
      })

      if ('error' in result) {
        return ApiResponses.badRequest(result.error)
      }

      uploads.push(result as UploadUrlResponse)
    }

    return ApiResponses.success({ uploads } as MultipleUploadUrlResponse)
  } catch (error) {
    return handleApiError(error)
  }
}
